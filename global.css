/* =============== GOOGLE FONTS =============== */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

/* =============== VARIABLES CSS =============== */
:root {
  --header-height: 3.5rem;

  /* ========== Colors ========== */
  /* Color mode HSL(hue, saturation, lightness) */
  --first-color: hsl(14, 98%, 50%);
  --first-color-alt: hsl(14, 98%, 44%);
  --first-color-light: hsl(14, 98%, 58%);
  --second-color: hsl(195, 75%, 52%);
  --title-color: hsl(0, 0%, 13%);
  --text-color: hsl(0, 0%, 35%);
  --text-color-light: hsl(0, 0%, 64%);
  --body-color: hsl(0, 0%, 100%);
  --container-color: hsl(0, 0%, 100%);
  --white-color: hsl(0, 0%, 100%);
  --shadow-color: hsla(0, 0%, 0%, 0.1);
  --border-color: hsl(0, 0%, 94%);

  /* ========== Font and typography ========== */
  --body-font: 'Cairo', sans-serif;
  --biggest-font-size: 2.5rem;
  --h1-font-size: 1.75rem;
  --h2-font-size: 1.25rem;
  --h3-font-size: 1rem;
  --normal-font-size: .938rem;
  --small-font-size: .813rem;
  --smaller-font-size: .75rem;

  /* ========== Font weight ========== */
  --font-light: 300;
  --font-regular: 400;
  --font-medium: 500;
  --font-semi-bold: 600;
  --font-bold: 700;

  /* ========== z index ========== */
  --z-tooltip: 10;
  --z-fixed: 100;
  --z-modal: 1000;
}

/* ========== Responsive typography ========== */
@media screen and (min-width: 1150px) {
  :root {
    --biggest-font-size: 3.5rem;
    --h1-font-size: 2.25rem;
    --h2-font-size: 1.5rem;
    --h3-font-size: 1.25rem;
    --normal-font-size: 1rem;
    --small-font-size: .875rem;
    --smaller-font-size: .813rem;
  }
}

/* ========== Dark theme variables ========== */
body.dark-theme {
  --title-color: hsl(0, 0%, 95%);
  --text-color: hsl(0, 0%, 70%);
  --text-color-light: hsl(0, 0%, 60%);
  --body-color: hsl(0, 0%, 8%);
  --container-color: hsl(0, 0%, 12%);
  --border-color: hsl(0, 0%, 20%);
  --shadow-color: hsla(0, 0%, 0%, 0.4);
}

/* =============== BASE =============== */
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--body-font);
  font-size: var(--normal-font-size);
  background-color: var(--body-color);
  color: var(--text-color);
  transition: background-color .4s, color .4s;
}

h1, h2, h3, h4 {
  color: var(--title-color);
  font-weight: var(--font-semi-bold);
}

ul {
  list-style: none;
}

a {
  text-decoration: none;
}

img {
  display: block;
  max-width: 100%;
  height: auto;
}

input, button, textarea, select {
  font-family: var(--body-font);
  font-size: var(--normal-font-size);
}

button {
  border: none;
  outline: none;
  cursor: pointer;
}

/* =============== REUSABLE CSS CLASSES =============== */
.container {
  max-width: 1120px;
  margin-inline: 1.5rem;
}

.grid {
  display: grid;
  gap: 1.5rem;
}

.section {
  padding-block: 5rem 1rem;
}

.section__title {
  font-size: var(--h1-font-size);
  font-weight: var(--font-bold);
  text-align: center;
  margin-bottom: 2rem;
  color: var(--title-color);
}

.main {
  overflow: hidden;
}

/* =============== BUTTONS =============== */
.btn {
  display: inline-flex;
  align-items: center;
  column-gap: .5rem;
  background-color: var(--first-color);
  color: var(--white-color);
  padding: 1rem 1.5rem;
  border-radius: .75rem;
  font-weight: var(--font-medium);
  transition: background-color .4s;
  border: none;
  cursor: pointer;
  text-decoration: none;
}

.btn:hover {
  background-color: var(--first-color-alt);
}

.btn--secondary {
  background-color: transparent;
  color: var(--first-color);
  border: 2px solid var(--first-color);
}

.btn--secondary:hover {
  background-color: var(--first-color);
  color: var(--white-color);
}

.btn--small {
  padding: .75rem 1rem;
  font-size: var(--small-font-size);
}

.btn--full {
  width: 100%;
  justify-content: center;
}

/* =============== HEADER & NAV =============== */
.header {
  position: fixed;
  width: 100%;
  top: 0;
  left: 0;
  background-color: var(--container-color);
  z-index: var(--z-fixed);
  transition: box-shadow .4s, background-color .4s, backdrop-filter .4s;
  box-shadow: 0 2px 16px var(--shadow-color);
}

.scroll-header {
  backdrop-filter: blur(10px);
  background-color: hsla(0, 0%, 100%, 0.9);
}

body.dark-theme .scroll-header {
  background-color: hsla(0, 0%, 12%, 0.9);
}

.nav {
  height: var(--header-height);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav__logo {
  color: var(--title-color);
  font-weight: var(--font-bold);
  font-size: var(--h2-font-size);
}

.nav__menu {
  display: flex;
  align-items: center;
}

.nav__list {
  display: flex;
  column-gap: 2rem;
}

.nav__link {
  color: var(--text-color);
  font-weight: var(--font-medium);
  transition: color .4s;
}

.nav__link:hover,
.active-link {
  color: var(--first-color);
}

.nav__icons {
  display: flex;
  align-items: center;
  column-gap: 1rem;
}

.nav__icon {
  position: relative;
  background: none;
  border: none;
  font-size: 1.25rem;
  color: var(--title-color);
  cursor: pointer;
  transition: color .4s;
}

.nav__icon:hover {
  color: var(--first-color);
}

.nav__count {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: var(--first-color);
  color: var(--white-color);
  font-size: var(--smaller-font-size);
  font-weight: var(--font-semi-bold);
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav__toggle {
  display: none;
}

/* =============== HERO =============== */
.hero {
  padding-top: calc(var(--header-height) + 2rem);
  background: linear-gradient(135deg, var(--first-color-light), var(--second-color));
  color: var(--white-color);
  text-align: center;
}

.hero__container {
  padding-block: 4rem;
}

.hero__title {
  font-size: var(--biggest-font-size);
  font-weight: var(--font-bold);
  margin-bottom: 1rem;
}

.hero__description {
  font-size: var(--h3-font-size);
  margin-bottom: 2rem;
  opacity: .9;
}

.hero__buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  column-gap: 1rem;
  flex-wrap: wrap;
}

/* =============== PRODUCTS =============== */
.products__container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.product__card {
  background-color: var(--container-color);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 16px var(--shadow-color);
  transition: transform .4s, box-shadow .4s;
  border: 1px solid var(--border-color);
}

.product__card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px var(--shadow-color);
}

.product__image {
  position: relative;
  margin-bottom: 1rem;
}

.product__img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: .75rem;
}

.product__favorite {
  position: absolute;
  top: .75rem;
  right: .75rem;
  background-color: var(--white-color);
  border: none;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: var(--text-color);
  cursor: pointer;
  transition: all .4s;
  box-shadow: 0 2px 8px var(--shadow-color);
}

.product__favorite:hover,
.product__favorite.active {
  color: var(--first-color);
  transform: scale(1.1);
}

.product__title {
  font-size: var(--h2-font-size);
  font-weight: var(--font-semi-bold);
  margin-bottom: .5rem;
  color: var(--title-color);
}

.product__description {
  font-size: var(--small-font-size);
  color: var(--text-color-light);
  margin-bottom: 1rem;
  line-height: 1.5;
}

.product__options {
  margin-bottom: 1rem;
}

.product__label {
  font-size: var(--small-font-size);
  font-weight: var(--font-medium);
  color: var(--title-color);
  margin-bottom: .5rem;
  display: block;
}

.product__select {
  width: 100%;
  padding: .5rem;
  border: 1px solid var(--border-color);
  border-radius: .5rem;
  background-color: var(--container-color);
  color: var(--text-color);
  font-size: var(--small-font-size);
}

.product__footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product__price {
  font-size: var(--h3-font-size);
  font-weight: var(--font-bold);
  color: var(--first-color);
}

/* =============== ABOUT =============== */
.about__container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.about__card {
  background-color: var(--container-color);
  padding: 2rem 1.5rem;
  border-radius: 1rem;
  text-align: center;
  box-shadow: 0 4px 16px var(--shadow-color);
  transition: transform .4s;
  border: 1px solid var(--border-color);
}

.about__card:hover {
  transform: translateY(-4px);
}

.about__icon {
  background: linear-gradient(135deg, var(--first-color), var(--first-color-alt));
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  font-size: 1.5rem;
  color: var(--white-color);
}

.about__title {
  font-size: var(--h2-font-size);
  font-weight: var(--font-semi-bold);
  margin-bottom: .75rem;
  color: var(--title-color);
}

.about__description {
  font-size: var(--small-font-size);
  color: var(--text-color-light);
  line-height: 1.6;
}

/* =============== CONTACT =============== */
.contact__container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  margin-top: 2rem;
}

.contact__info {
  background-color: var(--container-color);
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 4px 16px var(--shadow-color);
  border: 1px solid var(--border-color);
}

.contact__title {
  font-size: var(--h2-font-size);
  font-weight: var(--font-semi-bold);
  margin-bottom: 1.5rem;
  color: var(--title-color);
}

.contact__item {
  display: flex;
  align-items: center;
  column-gap: 1rem;
  margin-bottom: 1.5rem;
}

.contact__item i {
  font-size: 1.25rem;
  color: var(--first-color);
}

.contact__item div {
  display: flex;
  flex-direction: column;
}

.contact__label {
  font-size: var(--small-font-size);
  font-weight: var(--font-medium);
  color: var(--title-color);
}

.contact__value {
  font-size: var(--normal-font-size);
  color: var(--text-color);
}

.contact__social {
  margin-top: 2rem;
}

.contact__social-title {
  font-size: var(--h3-font-size);
  font-weight: var(--font-medium);
  margin-bottom: 1rem;
  color: var(--title-color);
}

.contact__social-links {
  display: flex;
  column-gap: 1rem;
}

.contact__social-link {
  background-color: var(--first-color);
  color: var(--white-color);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  transition: background-color .4s, transform .4s;
}

.contact__social-link:hover {
  background-color: var(--first-color-alt);
  transform: translateY(-2px);
}

.contact__form {
  background-color: var(--container-color);
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 4px 16px var(--shadow-color);
  border: 1px solid var(--border-color);
}

.contact__form-title {
  font-size: var(--h2-font-size);
  font-weight: var(--font-semi-bold);
  margin-bottom: 1.5rem;
  color: var(--title-color);
}

.contact__form-group {
  margin-bottom: 1.5rem;
}

.contact__input,
.contact__textarea {
  width: 100%;
  padding: 1rem;
  border: 1px solid var(--border-color);
  border-radius: .5rem;
  background-color: var(--body-color);
  color: var(--text-color);
  font-size: var(--normal-font-size);
  transition: border-color .4s;
}

.contact__input:focus,
.contact__textarea:focus {
  outline: none;
  border-color: var(--first-color);
}

.contact__textarea {
  resize: vertical;
  min-height: 120px;
}

/* =============== INFO CENTER =============== */
.info__section {
  margin-bottom: 3rem;
}

.info__subtitle {
  font-size: var(--h2-font-size);
  font-weight: var(--font-semi-bold);
  color: var(--title-color);
  margin-bottom: 1.5rem;
  text-align: center;
  position: relative;
}

.info__subtitle::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(135deg, var(--first-color), var(--first-color-alt));
  border-radius: 2px;
}

.info__content {
  background-color: var(--container-color);
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 4px 16px var(--shadow-color);
  border: 1px solid var(--border-color);
}

.info__list {
  list-style: none;
  padding: 0;
}

.info__list li {
  position: relative;
  padding: 0.75rem 0 0.75rem 2rem;
  color: var(--text-color);
  line-height: 1.6;
  border-bottom: 1px solid var(--border-color);
}

.info__list li:last-child {
  border-bottom: none;
}

.info__list li::before {
  content: '✓';
  position: absolute;
  left: 0;
  top: 0.75rem;
  color: var(--first-color);
  font-weight: var(--font-bold);
  font-size: 1.1rem;
}

/* =============== FAQ =============== */
.faq__container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.faq__item {
  background-color: var(--container-color);
  border-radius: 1rem;
  box-shadow: 0 4px 16px var(--shadow-color);
  border: 1px solid var(--border-color);
  overflow: hidden;
  transition: transform .4s;
}

.faq__item:hover {
  transform: translateY(-2px);
}

.faq__question {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  cursor: pointer;
  transition: background-color .4s;
}

.faq__question:hover {
  background-color: var(--body-color);
}

.faq__question h4 {
  font-size: var(--h3-font-size);
  font-weight: var(--font-medium);
  color: var(--title-color);
  margin: 0;
}

.faq__icon {
  font-size: 1.25rem;
  color: var(--first-color);
  transition: transform .4s;
}

.faq__item.active .faq__icon {
  transform: rotate(45deg);
}

.faq__answer {
  max-height: 0;
  overflow: hidden;
  transition: max-height .4s ease, padding .4s ease;
  background-color: var(--body-color);
}

.faq__item.active .faq__answer {
  max-height: 300px;
  padding: 0 1.5rem 1.5rem;
}

.faq__answer p {
  color: var(--text-color);
  line-height: 1.6;
  margin-bottom: 0.5rem;
}

.faq__answer ul {
  list-style: none;
  padding: 0;
  margin: 0.5rem 0;
}

.faq__answer ul li {
  position: relative;
  padding: 0.25rem 0 0.25rem 1.5rem;
  color: var(--text-color);
}

.faq__answer ul li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--first-color);
  font-weight: var(--font-bold);
}

.faq__answer strong {
  color: var(--title-color);
  font-weight: var(--font-semi-bold);
}

/* =============== TERMS =============== */
.terms__group {
  margin-bottom: 2rem;
}

.terms__group:last-child {
  margin-bottom: 0;
}

.terms__title {
  font-size: var(--h3-font-size);
  font-weight: var(--font-semi-bold);
  color: var(--first-color);
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--border-color);
}

/* =============== NOTICE BOX =============== */
.notice__box {
  background: linear-gradient(135deg, var(--first-color-light), var(--second-color));
  color: var(--white-color);
  padding: 2rem;
  border-radius: 1rem;
  position: relative;
  overflow: hidden;
}

.notice__box::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: rotate(45deg);
}

.notice__box .info__list li {
  color: var(--white-color);
  border-bottom-color: rgba(255, 255, 255, 0.2);
}

.notice__box .info__list li::before {
  color: var(--white-color);
}

/* =============== FOOTER =============== */
.footer {
  background-color: var(--title-color);
  color: var(--white-color);
  padding-block: 3rem 1rem;
}

.footer__container {
  margin-bottom: 2rem;
}

.footer__content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.footer__title {
  font-size: var(--h2-font-size);
  font-weight: var(--font-semi-bold);
  margin-bottom: 1rem;
  color: var(--white-color);
}

.footer__description {
  font-size: var(--small-font-size);
  line-height: 1.6;
  margin-bottom: 1.5rem;
  opacity: .8;
}

.footer__social {
  display: flex;
  column-gap: 1rem;
}

.footer__social-link {
  background-color: var(--first-color);
  color: var(--white-color);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  transition: background-color .4s, transform .4s;
}

.footer__social-link:hover {
  background-color: var(--first-color-alt);
  transform: translateY(-2px);
}

.footer__list {
  display: flex;
  flex-direction: column;
  row-gap: .75rem;
}

.footer__link {
  color: var(--white-color);
  font-size: var(--small-font-size);
  transition: color .4s;
  opacity: .8;
}

.footer__link:hover {
  color: var(--first-color-light);
  opacity: 1;
}

.footer__item {
  display: flex;
  align-items: center;
  column-gap: .5rem;
  font-size: var(--small-font-size);
  opacity: .8;
}

.footer__item i {
  font-size: 1rem;
  color: var(--first-color);
}

.footer__bottom {
  border-top: 1px solid hsla(0, 0%, 100%, .1);
  padding-top: 1rem;
  text-align: center;
}

.footer__copy {
  font-size: var(--small-font-size);
  opacity: .8;
}

/* =============== SIDEBAR =============== */
.sidebar {
  position: fixed;
  top: 0;
  width: 350px;
  height: 100vh;
  background-color: var(--container-color);
  z-index: var(--z-modal);
  transition: transform .4s;
  display: flex;
  flex-direction: column;
  box-shadow: 0 0 32px var(--shadow-color);
}

.cart-sidebar {
  left: -350px;
}

.cart-sidebar.show {
  transform: translateX(350px);
}

.favorites-sidebar {
  right: -350px;
}

.favorites-sidebar.show {
  transform: translateX(-350px);
}

.sidebar__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.sidebar__title {
  font-size: var(--h2-font-size);
  font-weight: var(--font-semi-bold);
  color: var(--title-color);
}

.sidebar__close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-color);
  cursor: pointer;
  transition: color .4s;
}

.sidebar__close:hover {
  color: var(--first-color);
}

.sidebar__content {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
}

.sidebar__footer {
  padding: 1.5rem;
  border-top: 1px solid var(--border-color);
}

.cart-empty,
.favorites-empty {
  text-align: center;
  padding: 2rem 0;
  color: var(--text-color-light);
}

.cart-empty i,
.favorites-empty i {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
}

.cart-total {
  font-size: var(--h3-font-size);
  font-weight: var(--font-semi-bold);
  color: var(--title-color);
  margin-bottom: 1rem;
  text-align: center;
}

.cart-item,
.favorite-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background-color: var(--body-color);
  border-radius: .75rem;
  margin-bottom: 1rem;
  border: 1px solid var(--border-color);
}

.cart-item__image,
.favorite-item__image {
  width: 60px;
  height: 60px;
  border-radius: .5rem;
  object-fit: cover;
}

.cart-item__content,
.favorite-item__content {
  flex: 1;
}

.cart-item__title,
.favorite-item__title {
  font-size: var(--normal-font-size);
  font-weight: var(--font-medium);
  color: var(--title-color);
  margin-bottom: .25rem;
}

.cart-item__option,
.favorite-item__option {
  font-size: var(--small-font-size);
  color: var(--text-color-light);
  margin-bottom: .25rem;
}

.cart-item__price,
.favorite-item__price {
  font-size: var(--small-font-size);
  font-weight: var(--font-semi-bold);
  color: var(--first-color);
}

.cart-item__controls {
  display: flex;
  align-items: center;
  gap: .5rem;
}

.cart-item__btn {
  background-color: var(--first-color);
  color: var(--white-color);
  border: none;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: .875rem;
  cursor: pointer;
  transition: background-color .4s;
}

.cart-item__btn:hover {
  background-color: var(--first-color-alt);
}

.cart-item__quantity {
  font-size: var(--normal-font-size);
  font-weight: var(--font-medium);
  color: var(--title-color);
  min-width: 20px;
  text-align: center;
}

.favorite-item__remove {
  background: none;
  border: none;
  color: var(--text-color-light);
  font-size: 1.25rem;
  cursor: pointer;
  transition: color .4s;
}

.favorite-item__remove:hover {
  color: var(--first-color);
}

/* =============== MODAL =============== */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: hsla(0, 0%, 0%, .5);
  z-index: var(--z-modal);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity .4s, visibility .4s;
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

.modal__content {
  background-color: var(--container-color);
  border-radius: 1rem;
  padding: 2rem;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 8px 32px var(--shadow-color);
}

.modal__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.modal__title {
  font-size: var(--h2-font-size);
  font-weight: var(--font-semi-bold);
  color: var(--title-color);
}

.modal__close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-color);
  cursor: pointer;
  transition: color .4s;
}

.modal__close:hover {
  color: var(--first-color);
}

.form__group {
  margin-bottom: 1.5rem;
}

.form__label {
  display: block;
  font-size: var(--small-font-size);
  font-weight: var(--font-medium);
  color: var(--title-color);
  margin-bottom: .5rem;
}

.form__input,
.form__select,
.form__textarea {
  width: 100%;
  padding: .75rem;
  border: 1px solid var(--border-color);
  border-radius: .5rem;
  background-color: var(--body-color);
  color: var(--text-color);
  font-size: var(--normal-font-size);
  transition: border-color .4s;
}

.form__input:focus,
.form__select:focus,
.form__textarea:focus {
  outline: none;
  border-color: var(--first-color);
}

.form__textarea {
  resize: vertical;
  min-height: 80px;
}

.payment-banner {
  background: linear-gradient(135deg, var(--first-color), var(--first-color-alt));
  color: var(--white-color);
  padding: 1rem;
  border-radius: .75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: .5rem;
  margin-bottom: 1.5rem;
  font-weight: var(--font-medium);
}

.payment-banner i {
  font-size: 1.25rem;
}

.order-summary {
  background-color: var(--body-color);
  padding: 1.5rem;
  border-radius: .75rem;
  margin-bottom: 1.5rem;
  border: 1px solid var(--border-color);
}

.order-summary__item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: .75rem;
  font-size: var(--normal-font-size);
}

.order-summary__item:last-child {
  margin-bottom: 0;
}

.order-summary__total {
  border-top: 1px solid var(--border-color);
  padding-top: .75rem;
  font-weight: var(--font-semi-bold);
  font-size: var(--h3-font-size);
  color: var(--title-color);
}

.success-content {
  text-align: center;
  padding: 2rem 0;
}

.success-icon {
  background: linear-gradient(135deg, var(--first-color), var(--first-color-alt));
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  font-size: 2rem;
  color: var(--white-color);
}

.success-title {
  font-size: var(--h1-font-size);
  font-weight: var(--font-bold);
  color: var(--title-color);
  margin-bottom: 1rem;
}

.success-message {
  font-size: var(--normal-font-size);
  color: var(--text-color);
  line-height: 1.6;
  margin-bottom: 2rem;
}

/* =============== OVERLAY =============== */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: hsla(0, 0%, 0%, .3);
  z-index: calc(var(--z-modal) - 1);
  opacity: 0;
  visibility: hidden;
  transition: opacity .4s, visibility .4s;
}

.overlay.show {
  opacity: 1;
  visibility: visible;
}

/* =============== BREAKPOINTS =============== */
/* For small devices */
@media screen and (max-width: 340px) {
  .container {
    margin-inline: 1rem;
  }

  .hero__buttons {
    flex-direction: column;
    gap: 1rem;
  }

  .sidebar {
    width: 100%;
  }

  .cart-sidebar {
    left: -100%;
  }

  .cart-sidebar.show {
    transform: translateX(100%);
  }

  .favorites-sidebar {
    right: -100%;
  }

  .favorites-sidebar.show {
    transform: translateX(-100%);
  }
}

/* For medium devices */
@media screen and (max-width: 768px) {
  .nav__menu {
    position: fixed;
    top: var(--header-height);
    left: -100%;
    background-color: var(--container-color);
    width: 100%;
    height: calc(100vh - var(--header-height));
    padding: 2rem 1.5rem;
    transition: left .4s;
    box-shadow: 0 2px 16px var(--shadow-color);
  }

  .nav__menu.show {
    left: 0;
  }

  .nav__list {
    flex-direction: column;
    row-gap: 2rem;
  }

  .nav__toggle {
    display: block;
    font-size: 1.25rem;
    color: var(--title-color);
    cursor: pointer;
  }

  .contact__container {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .products__container {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  .about__container {
    grid-template-columns: 1fr;
  }

  .footer__content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .modal__content {
    width: 95%;
    padding: 1.5rem;
  }
}

/* For large devices */
@media screen and (min-width: 1150px) {
  .container {
    margin-inline: auto;
  }

  .section {
    padding-block: 7rem 2rem;
  }

  .nav {
    height: calc(var(--header-height) + 1.5rem);
  }

  .nav__list {
    column-gap: 3rem;
  }

  .nav__icons {
    column-gap: 1.5rem;
  }

  .hero__container {
    padding-block: 6rem;
  }

  .hero__title {
    margin-bottom: 1.5rem;
  }

  .hero__description {
    margin-bottom: 3rem;
  }

  .products__container {
    grid-template-columns: repeat(3, 1fr);
    gap: 3rem;
  }

  .product__card {
    padding: 2rem;
  }

  .about__container {
    grid-template-columns: repeat(3, 1fr);
    gap: 3rem;
  }

  .about__card {
    padding: 3rem 2rem;
  }

  .contact__container {
    gap: 4rem;
  }

  .contact__info,
  .contact__form {
    padding: 3rem;
  }

  .footer__content {
    grid-template-columns: 2fr 1fr 1fr;
  }

  .sidebar {
    width: 400px;
  }

  .cart-sidebar {
    left: -400px;
  }

  .cart-sidebar.show {
    transform: translateX(400px);
  }

  .favorites-sidebar {
    right: -400px;
  }

  .favorites-sidebar.show {
    transform: translateX(-400px);
  }
}

/* =============== SCROLL BAR =============== */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background-color: var(--border-color);
}

::-webkit-scrollbar-thumb {
  background-color: var(--first-color);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--first-color-alt);
}

/* =============== SCROLL UP =============== */
.scrollup {
  position: fixed;
  right: 1rem;
  bottom: -50%;
  background-color: var(--first-color);
  color: var(--white-color);
  width: 45px;
  height: 45px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  z-index: var(--z-tooltip);
  transition: bottom .4s, transform .4s, background-color .4s;
}

.scrollup:hover {
  transform: translateY(-4px);
  background-color: var(--first-color-alt);
}

.show-scroll {
  bottom: 3rem;
}

/* =============== LOADING SCREEN =============== */
.loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, var(--first-color), var(--second-color));
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity .5s, visibility .5s;
}

.loading.hide {
  opacity: 0;
  visibility: hidden;
}

.loading__content {
  text-align: center;
  color: var(--white-color);
}

.loading__logo {
  font-size: 2.5rem;
  font-weight: var(--font-bold);
  margin-bottom: 2rem;
  animation: pulse 2s infinite;
}

.loading__spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid var(--white-color);
  border-radius: 50%;
  margin: 0 auto 1rem;
  animation: spin 1s linear infinite;
}

.loading__text {
  font-size: var(--normal-font-size);
  opacity: 0.9;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* =============== ANIMATIONS =============== */
.fade-in {
  animation: fadeIn 0.6s ease-in-out;
}

.slide-up {
  animation: slideUp 0.6s ease-out;
}

.scale-in {
  animation: scaleIn 0.4s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* =============== SMOOTH TRANSITIONS =============== */
* {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, transform 0.3s ease;
}

/* =============== PERFORMANCE OPTIMIZATIONS =============== */
.product__img,
.cart-item__image,
.favorite-item__image {
  will-change: transform;
}

.sidebar,
.modal {
  will-change: transform, opacity;
}

.btn {
  will-change: background-color, transform;
}
