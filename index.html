<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VelaSweets - متجر الحلويات الفاخرة</title>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- RemixIcon -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">

    <!-- Global CSS -->
    <link rel="stylesheet" href="./global.css">
    <style>
        /* Fallback CSS in case external file doesn't load */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Cairo', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #fff;
        }

        .container {
            max-width: 1120px;
            margin: 0 auto;
            padding: 0 1.5rem;
        }

        .header {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            background: #fff;
            box-shadow: 0 2px 16px rgba(0,0,0,0.1);
            z-index: 100;
        }

        .nav {
            height: 3.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav__logo h2 {
            color: #ff6b35;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .nav__list {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav__link {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav__link:hover {
            color: #ff6b35;
        }

        .nav__icons {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .nav__icon {
            background: none;
            border: none;
            font-size: 1.25rem;
            color: #333;
            cursor: pointer;
            position: relative;
        }

        .nav__count {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #ff6b35;
            color: white;
            font-size: 0.75rem;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .main {
            margin-top: 3.5rem;
        }

        .hero {
            background: linear-gradient(135deg, #ff6b35, #4ecdc4);
            color: white;
            text-align: center;
            padding: 4rem 0;
        }

        .hero__title {
            font-size: 3rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }

        .hero__description {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .btn {
            display: inline-block;
            background: #ff6b35;
            color: white;
            padding: 1rem 2rem;
            text-decoration: none;
            border-radius: 0.75rem;
            font-weight: 500;
            margin: 0.5rem;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #e55a2b;
        }

        .btn--secondary {
            background: transparent;
            border: 2px solid white;
        }

        .section {
            padding: 5rem 0;
        }

        .section__title {
            text-align: center;
            font-size: 2rem;
            margin-bottom: 3rem;
            color: #333;
        }

        .products__container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .product__card {
            background: white;
            border-radius: 1rem;
            padding: 1.5rem;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }

        .product__card:hover {
            transform: translateY(-4px);
        }

        .product__img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 0.75rem;
            margin-bottom: 1rem;
        }

        .product__title {
            font-size: 1.25rem;
            margin-bottom: 0.5rem;
            color: #333;
        }

        .product__price {
            color: #ff6b35;
            font-weight: 700;
            font-size: 1.1rem;
        }

        .footer {
            background: #333;
            color: white;
            padding: 3rem 0 1rem;
        }

        .footer__content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer__title {
            font-size: 1.25rem;
            margin-bottom: 1rem;
        }

        .footer__copy {
            text-align: center;
            padding-top: 1rem;
            border-top: 1px solid #555;
            opacity: 0.8;
        }

        /* Additional Styles */
        .about__container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }

        .about__card {
            background: white;
            padding: 2rem;
            border-radius: 1rem;
            text-align: center;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }

        .about__icon {
            background: linear-gradient(135deg, #ff6b35, #e55a2b);
            width: 64px;
            height: 64px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 1.5rem;
        }

        .contact__container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
        }

        .contact__info, .contact__form {
            background: white;
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }

        .contact__input, .contact__textarea {
            width: 100%;
            padding: 1rem;
            border: 1px solid #ddd;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            font-family: inherit;
        }

        .product__favorite {
            position: absolute;
            top: 0.75rem;
            right: 0.75rem;
            background: white;
            border: none;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .product__image {
            position: relative;
        }

        .product__select {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }

        .product__footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .btn--small {
            padding: 0.75rem 1rem;
            font-size: 0.875rem;
        }

        .loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            background: linear-gradient(135deg, #ff6b35, #4ecdc4);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
        }

        .loading.hide {
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.5s, visibility 0.5s;
        }

        .loading__logo {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 2rem;
        }

        .loading__spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255,255,255,0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            margin: 0 auto 1rem;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .nav__menu {
                display: none;
            }

            .hero__title {
                font-size: 2rem;
            }

            .products__container {
                grid-template-columns: 1fr;
            }

            .contact__container {
                grid-template-columns: 1fr;
            }

            .about__container {
                grid-template-columns: 1fr;
            }
        }

        /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: 0;
            width: 350px;
            height: 100vh;
            background: white;
            z-index: 1000;
            transform: translateX(-100%);
            transition: transform 0.4s;
            box-shadow: 0 0 32px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
        }

        .cart-sidebar {
            left: 0;
        }

        .favorites-sidebar {
            right: 0;
            transform: translateX(100%);
        }

        .sidebar.show {
            transform: translateX(0);
        }

        .sidebar__header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem;
            border-bottom: 1px solid #eee;
        }

        .sidebar__close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
        }

        .sidebar__content {
            flex: 1;
            padding: 1.5rem;
            overflow-y: auto;
        }

        .sidebar__footer {
            padding: 1.5rem;
            border-top: 1px solid #eee;
        }

        .cart-empty, .favorites-empty {
            text-align: center;
            padding: 2rem 0;
            color: #999;
        }

        .cart-empty i, .favorites-empty i {
            font-size: 3rem;
            margin-bottom: 1rem;
            display: block;
        }

        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            background: rgba(0,0,0,0.5);
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.4s, visibility 0.4s;
        }

        .overlay.show {
            opacity: 1;
            visibility: visible;
        }

        /* Modal Styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.4s, visibility 0.4s;
        }

        .modal.show {
            opacity: 1;
            visibility: visible;
        }

        .modal__content {
            background: white;
            border-radius: 1rem;
            padding: 2rem;
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
        }

        .form__group {
            margin-bottom: 1.5rem;
        }

        .form__label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form__input, .form__select, .form__textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 0.5rem;
            font-family: inherit;
        }

        .btn--full {
            width: 100%;
            text-align: center;
        }

        /* FAQ Styles */
        .faq__item {
            background: white;
            border-radius: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .faq__question {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem;
            cursor: pointer;
            background: white;
        }

        .faq__question:hover {
            background: #f8f9fa;
        }

        .faq__answer {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.4s ease;
            background: #f8f9fa;
        }

        .faq__item.active .faq__answer {
            max-height: 300px;
            padding: 0 1.5rem 1.5rem;
        }

        .info__list {
            list-style: none;
            padding: 0;
        }

        .info__list li {
            padding: 0.75rem 0;
            border-bottom: 1px solid #eee;
            position: relative;
            padding-left: 2rem;
        }

        .info__list li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #ff6b35;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div class="loading" id="loading">
        <div class="loading__content">
            <div class="loading__logo">VelaSweets</div>
            <div class="loading__spinner"></div>
            <p class="loading__text">جاري التحميل...</p>
        </div>
    </div>
    <!-- Header -->
    <header class="header">
        <nav class="nav container">
            <!-- Logo -->
            <div class="nav__logo">
                <h2>VelaSweets</h2>
            </div>

            <!-- Navigation Links -->
            <div class="nav__menu" id="nav-menu">
                <ul class="nav__list">
                    <li class="nav__item">
                        <a href="#home" class="nav__link active-link">الرئيسية</a>
                    </li>
                    <li class="nav__item">
                        <a href="#products" class="nav__link">المنتجات</a>
                    </li>
                    <li class="nav__item">
                        <a href="#info" class="nav__link">مركز المعلومات</a>
                    </li>
                </ul>
            </div>

            <!-- Navigation Icons -->
            <div class="nav__icons">
                <button class="nav__icon theme-toggle" id="theme-toggle">
                    <i class="ri-moon-line"></i>
                </button>
                <button class="nav__icon favorites-toggle" id="favorites-toggle">
                    <i class="ri-heart-line"></i>
                    <span class="nav__count" id="favorites-count">0</span>
                </button>
                <button class="nav__icon cart-toggle" id="cart-toggle">
                    <i class="ri-shopping-cart-line"></i>
                    <span class="nav__count" id="cart-count">0</span>
                </button>
                <button class="nav__icon profile-toggle">
                    <i class="ri-user-line"></i>
                </button>
                <button class="nav__toggle" id="nav-toggle">
                    <i class="ri-menu-line"></i>
                </button>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main">
        <!-- Hero Section -->
        <section class="hero section" id="home">
            <div class="hero__container container">
                <div class="hero__content">
                    <h1 class="hero__title">VelaSweets</h1>
                    <p class="hero__description">
                        متجر الحلويات الفاخرة - نقدم لكم أجود أنواع الحلويات المصنوعة بحب وعناية خاصة
                    </p>
                    <div class="hero__buttons">
                        <a href="#products" class="btn btn--primary">شاهد المنتجات</a>
                        <a href="#about" class="btn btn--secondary">من نحن</a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Products Section -->
        <section class="products section" id="products">
            <div class="container">
                <h2 class="section__title">منتجاتنا</h2>
                <div class="products__container">
                    <!-- Product 1: جوزية -->
                    <article class="product__card">
                        <div class="product__image">
                            <img src="images/placeholder.svg" alt="جوزية" class="product__img">
                            <button class="product__favorite" data-id="1">
                                <i class="ri-heart-line"></i>
                            </button>
                        </div>
                        <div class="product__content">
                            <h3 class="product__title">جوزية</h3>
                            <p class="product__description">قطع جوزية محشية ومغطاة بطبقة شوكولاتة فاخرة</p>
                            <div class="product__options">
                                <span class="product__label">النكهات:</span>
                                <select class="product__select" data-id="1">
                                    <option value="الكراميل">الكراميل</option>
                                    <option value="الشوكولاتة">الشوكولاتة</option>
                                    <option value="البستاشيو">البستاشيو</option>
                                </select>
                            </div>
                            <div class="product__footer">
                                <span class="product__price">7,000 دينار عراقي</span>
                                <button class="btn btn--small btn--primary add-to-cart" data-id="1">
                                    أضف للسلة
                                </button>
                            </div>
                        </div>
                    </article>

                    <!-- Product 2: مادلين كيك -->
                    <article class="product__card">
                        <div class="product__image">
                            <img src="images/placeholder.svg" alt="مادلين كيك" class="product__img">
                            <button class="product__favorite" data-id="2">
                                <i class="ri-heart-line"></i>
                            </button>
                        </div>
                        <div class="product__content">
                            <h3 class="product__title">مادلين كيك</h3>
                            <p class="product__description">كيك ناعم ومميز مغطى بشوكولاتة فاخرة ومزين بالبندق المحمص</p>
                            <div class="product__options">
                                <span class="product__label">الخيارات:</span>
                                <select class="product__select" data-id="2">
                                    <option value="بالبندق">بالبندق</option>
                                </select>
                            </div>
                            <div class="product__footer">
                                <span class="product__price">4,500 دينار عراقي</span>
                                <button class="btn btn--small btn--primary add-to-cart" data-id="2">
                                    أضف للسلة
                                </button>
                            </div>
                        </div>
                    </article>

                    <!-- Product 3: حلى ڤيلا -->
                    <article class="product__card">
                        <div class="product__image">
                            <img src="images/placeholder.svg" alt="حلى ڤيلا" class="product__img">
                            <button class="product__favorite" data-id="3">
                                <i class="ri-heart-line"></i>
                            </button>
                        </div>
                        <div class="product__content">
                            <h3 class="product__title">حلى ڤيلا</h3>
                            <p class="product__description">حلى فاخر بطبقات متعددة من الكريمة والفول السوداني المقرمش</p>
                            <div class="product__options">
                                <span class="product__label">الخيارات:</span>
                                <select class="product__select" data-id="3">
                                    <option value="بالفول السوداني">بالفول السوداني</option>
                                </select>
                            </div>
                            <div class="product__footer">
                                <span class="product__price">6,000 دينار عراقي</span>
                                <button class="btn btn--small btn--primary add-to-cart" data-id="3">
                                    أضف للسلة
                                </button>
                            </div>
                        </div>
                    </article>
                </div>
            </div>
        </section>

        <!-- About Section -->
        <section class="about section" id="about">
            <div class="container">
                <h2 class="section__title">من نحن</h2>
                <div class="about__container">
                    <div class="about__card">
                        <div class="about__icon">
                            <i class="ri-trophy-line"></i>
                        </div>
                        <h3 class="about__title">جودة عالية</h3>
                        <p class="about__description">نستخدم أجود المكونات الطبيعية لضمان أفضل طعم وجودة</p>
                    </div>
                    <div class="about__card">
                        <div class="about__icon">
                            <i class="ri-truck-line"></i>
                        </div>
                        <h3 class="about__title">توصيل سريع</h3>
                        <p class="about__description">نوصل طلباتكم بسرعة وأمان إلى جميع المحافظات العراقية</p>
                    </div>
                    <div class="about__card">
                        <div class="about__icon">
                            <i class="ri-phone-line"></i>
                        </div>
                        <h3 class="about__title">خدمة ممتازة</h3>
                        <p class="about__description">فريق خدمة العملاء متاح دائماً لمساعدتكم وتلبية احتياجاتكم</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section class="contact section" id="contact">
            <div class="container">
                <h2 class="section__title">تواصل معنا</h2>
                <div class="contact__container">
                    <div class="contact__info">
                        <h3 class="contact__title">معلومات الاتصال</h3>
                        <div class="contact__item">
                            <i class="ri-phone-line"></i>
                            <div>
                                <span class="contact__label">الهاتف</span>
                                <span class="contact__value">+964 ************</span>
                            </div>
                        </div>
                        <div class="contact__item">
                            <i class="ri-mail-line"></i>
                            <div>
                                <span class="contact__label">الإيميل</span>
                                <span class="contact__value"><EMAIL></span>
                            </div>
                        </div>
                        <div class="contact__item">
                            <i class="ri-map-pin-line"></i>
                            <div>
                                <span class="contact__label">العنوان</span>
                                <span class="contact__value">البصرة، العراق</span>
                            </div>
                        </div>
                        <div class="contact__social">
                            <h4 class="contact__social-title">تابعونا على</h4>
                            <div class="contact__social-links">
                                <a href="#" class="contact__social-link">
                                    <i class="ri-facebook-fill"></i>
                                </a>
                                <a href="#" class="contact__social-link">
                                    <i class="ri-instagram-line"></i>
                                </a>
                                <a href="#" class="contact__social-link">
                                    <i class="ri-tiktok-line"></i>
                                </a>
                            </div>
                        </div>
                    </div>

                    <form class="contact__form" id="contact-form">
                        <h3 class="contact__form-title">أرسل لنا رسالة</h3>
                        <div class="contact__form-group">
                            <input type="text" class="contact__input" placeholder="الاسم" required>
                        </div>
                        <div class="contact__form-group">
                            <input type="email" class="contact__input" placeholder="الإيميل" required>
                        </div>
                        <div class="contact__form-group">
                            <textarea class="contact__textarea" placeholder="الرسالة" rows="5" required></textarea>
                        </div>
                        <button type="submit" class="btn btn--primary">إرسال الرسالة</button>
                    </form>
                </div>
            </div>
        </section>

        <!-- Info Center Section -->
        <section class="info section" id="info">
            <div class="container">
                <h2 class="section__title">مركز المعلومات</h2>

                <!-- FAQ Section -->
                <div class="info__section">
                    <h3 class="info__subtitle">الأسئلة المتكررة</h3>
                    <div class="faq__container">
                        <div class="faq__item">
                            <div class="faq__question">
                                <h4>كيف يمكنني طلب منتجاتكم؟</h4>
                                <i class="ri-add-line faq__icon"></i>
                            </div>
                            <div class="faq__answer">
                                <p>يمكنك الطلب من خلال موقعنا الإلكتروني، بإضافة المنتجات إلى السلة ثم إتمام الطلب بعد التأكيد.</p>
                            </div>
                        </div>

                        <div class="faq__item">
                            <div class="faq__question">
                                <h4>كم تستغرق مدة التوصيل؟</h4>
                                <i class="ri-add-line faq__icon"></i>
                            </div>
                            <div class="faq__answer">
                                <p>تختلف مدة التوصيل حسب المحافظة:</p>
                                <ul>
                                    <li><strong>البصرة:</strong> من نفس اليوم إلى 24 ساعة</li>
                                    <li><strong>باقي المحافظات:</strong> من 2 إلى 3 أيام حسب المسافة</li>
                                </ul>
                            </div>
                        </div>

                        <div class="faq__item">
                            <div class="faq__question">
                                <h4>هل يمكنني إلغاء أو تعديل طلبي؟</h4>
                                <i class="ri-add-line faq__icon"></i>
                            </div>
                            <div class="faq__answer">
                                <p>نعم، يمكن الإلغاء أو التعديل خلال ساعة واحدة من تأكيد الطلب. بعد مرور الساعة يبدأ التحضير، ولا يُمكن التعديل أو الإلغاء بعد ذلك.</p>
                            </div>
                        </div>

                        <div class="faq__item">
                            <div class="faq__question">
                                <h4>ما هي طرق الدفع المتاحة؟</h4>
                                <i class="ri-add-line faq__icon"></i>
                            </div>
                            <div class="faq__answer">
                                <p>حاليًا نقبل الدفع عند الاستلام فقط. نعمل على توفير خيارات دفع إلكتروني قريبًا بإذن الله.</p>
                            </div>
                        </div>

                        <div class="faq__item">
                            <div class="faq__question">
                                <h4>هل تقدمون حلويات للمناسبات الخاصة؟</h4>
                                <i class="ri-add-line faq__icon"></i>
                            </div>
                            <div class="faq__answer">
                                <p>لا نقدم منتجات مخصصة للمناسبات حالياً، لكن يمكنك اختيار ما يناسبك من تشكيلتنا الجاهزة المناسبة للتجمعات الصغيرة.</p>
                            </div>
                        </div>

                        <div class="faq__item">
                            <div class="faq__question">
                                <h4>كم تبقى منتجاتكم طازجة؟</h4>
                                <i class="ri-add-line faq__icon"></i>
                            </div>
                            <div class="faq__answer">
                                <p>تبقى منتجاتنا طازجة لمدة 3 إلى 5 أيام إذا حُفظت في مكان بارد وجاف. للحصول على أفضل طعم، ننصح باستهلاكها خلال أول يومين.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Privacy Policy Section -->
                <div class="info__section">
                    <h3 class="info__subtitle">سياسة الخصوصية</h3>
                    <div class="info__content">
                        <ul class="info__list">
                            <li>نحتفظ ببياناتك الشخصية بسرية تامة</li>
                            <li>لا نشارك معلوماتك مع أي جهة خارجية</li>
                            <li>نستخدم البيانات لتحسين خدماتنا فقط</li>
                            <li>يمكنك طلب حذف بياناتك في أي وقت</li>
                        </ul>
                    </div>
                </div>

                <!-- Terms and Conditions Section -->
                <div class="info__section">
                    <h3 class="info__subtitle">الشروط والأحكام</h3>
                    <div class="info__content">
                        <div class="terms__group">
                            <h4 class="terms__title">شروط الطلب</h4>
                            <ul class="info__list">
                                <li>نبدأ بتحضير الطلب بعد تأكيد الزبون والموافقة على الشروط</li>
                                <li>جميع الطلبات تُحضّر حسب الطلب لضمان الطزاجة والجودة</li>
                                <li>يُفضل إرسال الطلب بوقت كافٍ لضمان توفر المنتج</li>
                                <li>لا يمكن إلغاء أو تعديل الطلب بعد بدء التحضير إلا باتفاق الطرفين</li>
                                <li>الطلبات تُنفذ حسب وقت استلامها</li>
                            </ul>
                        </div>

                        <div class="terms__group">
                            <h4 class="terms__title">شروط التوصيل</h4>
                            <ul class="info__list">
                                <li>تُضاف رسوم التوصيل إلى قيمة الطلب</li>
                                <li>يجب تواجد أحد لاستلام الطلب في الوقت المحدد</li>
                                <li>في حالة عدم التواجد، يُعاد الطلب ويُخصم فقط مبلغ رسوم التوصيل إذا تم إعلام الزبون بذلك مسبقًا</li>
                                <li>لا نتحمل مسؤولية التأخير الناتج عن ظروف خارجة عن إرادتنا كالأحوال الجوية أو الطرق</li>
                            </ul>
                        </div>

                        <div class="terms__group">
                            <h4 class="terms__title">شروط الإرجاع والاستبدال</h4>
                            <ul class="info__list">
                                <li>لا يُقبل الإرجاع أو الاستبدال إلا في حالة وجود عيب واضح في المنتج</li>
                                <li>يُفحص المنتج فور التسليم بحضور المندوب والزبون</li>
                                <li>يمكن للزبون التبليغ عن العيب خلال ساعة من وقت الاستلام</li>
                                <li>في حال ثبوت وجود عيب، يتم استرجاع المنتج مع تحمل الزبون رسوم التوصيل فقط</li>
                                <li>لا نتحمل مسؤولية تلف المنتج بعد التسليم وقبول الزبون له</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Important Notice Section -->
                <div class="info__section">
                    <h3 class="info__subtitle">تنويه مهم</h3>
                    <div class="info__content">
                        <div class="notice__box">
                            <ul class="info__list">
                                <li>جميع منتجاتنا مصنوعة من مكونات طبيعية</li>
                                <li>قد تحتوي على مكسرات أو مواد مسببة للحساسية</li>
                                <li>يرجى إبلاغنا عن أي حساسية غذائية عند الطلب</li>
                                <li>هذه الشروط قابلة للتعديل مع إعلام الزبائن</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer__container">
                <div class="footer__content">
                    <div class="footer__group">
                        <h3 class="footer__title">VelaSweets</h3>
                        <p class="footer__description">
                            متجر الحلويات الفاخرة - نقدم لكم أجود أنواع الحلويات المصنوعة بحب وعناية خاصة
                        </p>
                        <div class="footer__social">
                            <a href="#" class="footer__social-link">
                                <i class="ri-facebook-fill"></i>
                            </a>
                            <a href="#" class="footer__social-link">
                                <i class="ri-instagram-line"></i>
                            </a>
                            <a href="#" class="footer__social-link">
                                <i class="ri-tiktok-line"></i>
                            </a>
                        </div>
                    </div>

                    <div class="footer__group">
                        <h3 class="footer__title">روابط سريعة</h3>
                        <ul class="footer__list">
                            <li><a href="#home" class="footer__link">الرئيسية</a></li>
                            <li><a href="#products" class="footer__link">المنتجات</a></li>
                            <li><a href="#about" class="footer__link">من نحن</a></li>
                            <li><a href="#contact" class="footer__link">تواصل معنا</a></li>
                        </ul>
                    </div>

                    <div class="footer__group">
                        <h3 class="footer__title">معلومات الاتصال</h3>
                        <ul class="footer__list">
                            <li class="footer__item">
                                <i class="ri-phone-line"></i>
                                <span>+964 ************</span>
                            </li>
                            <li class="footer__item">
                                <i class="ri-mail-line"></i>
                                <span><EMAIL></span>
                            </li>
                            <li class="footer__item">
                                <i class="ri-map-pin-line"></i>
                                <span>البصرة، العراق</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="footer__bottom">
                <p class="footer__copy">
                    جميع الحقوق محفوظة © 2026 VelaSweets
                </p>
            </div>
        </div>
    </footer>

    <!-- Cart Sidebar -->
    <div class="sidebar cart-sidebar" id="cart-sidebar">
        <div class="sidebar__header">
            <h3 class="sidebar__title">السلة</h3>
            <button class="sidebar__close" id="cart-close">
                <i class="ri-close-line"></i>
            </button>
        </div>
        <div class="sidebar__content" id="cart-content">
            <div class="cart-empty">
                <i class="ri-shopping-cart-line"></i>
                <p>السلة فارغة</p>
            </div>
        </div>
        <div class="sidebar__footer">
            <div class="cart-total">
                <span>المجموع: <span id="cart-total">0</span> دينار عراقي</span>
            </div>
            <button class="btn btn--primary btn--full" id="order-now">اطلب الآن</button>
        </div>
    </div>

    <!-- Favorites Sidebar -->
    <div class="sidebar favorites-sidebar" id="favorites-sidebar">
        <div class="sidebar__header">
            <h3 class="sidebar__title">المفضلة</h3>
            <button class="sidebar__close" id="favorites-close">
                <i class="ri-close-line"></i>
            </button>
        </div>
        <div class="sidebar__content" id="favorites-content">
            <div class="favorites-empty">
                <i class="ri-heart-line"></i>
                <p>لا توجد منتجات مفضلة</p>
            </div>
        </div>
        <div class="sidebar__footer">
            <button class="btn btn--secondary btn--full" id="clear-favorites">مسح الكل</button>
        </div>
    </div>

    <!-- Order Modal -->
    <div class="modal" id="order-modal">
        <div class="modal__content">
            <div class="modal__header">
                <h3 class="modal__title">تأكيد الطلب</h3>
                <button class="modal__close" id="order-modal-close">
                    <i class="ri-close-line"></i>
                </button>
            </div>
            <form class="modal__form" id="order-form">
                <div class="form__group">
                    <label class="form__label">الاسم الكامل</label>
                    <input type="text" class="form__input" name="fullName" required>
                </div>
                <div class="form__group">
                    <label class="form__label">رقم الهاتف</label>
                    <input type="tel" class="form__input" name="phone" required>
                </div>
                <div class="form__group">
                    <label class="form__label">المحافظة</label>
                    <select class="form__select" name="province" required>
                        <option value="">اختر المحافظة</option>
                        <option value="البصرة">البصرة</option>
                        <option value="بغداد">بغداد</option>
                        <option value="أربيل">أربيل</option>
                        <option value="السليمانية">السليمانية</option>
                        <option value="دهوك">دهوك</option>
                        <option value="الموصل">الموصل</option>
                        <option value="كركوك">كركوك</option>
                        <option value="الأنبار">الأنبار</option>
                        <option value="النجف">النجف</option>
                        <option value="كربلاء">كربلاء</option>
                        <option value="بابل">بابل</option>
                        <option value="واسط">واسط</option>
                        <option value="صلاح الدين">صلاح الدين</option>
                        <option value="ديالى">ديالى</option>
                        <option value="المثنى">المثنى</option>
                        <option value="القادسية">القادسية</option>
                        <option value="ذي قار">ذي قار</option>
                        <option value="ميسان">ميسان</option>
                    </select>
                </div>
                <div class="form__group">
                    <label class="form__label">العنوان التفصيلي</label>
                    <textarea class="form__textarea" name="address" rows="3" required></textarea>
                </div>
                <div class="payment-banner">
                    <i class="ri-hand-coin-line"></i>
                    <span>الدفع عند الاستلام</span>
                </div>
                <div class="order-summary">
                    <div class="order-summary__item">
                        <span>المجموع الفرعي:</span>
                        <span id="order-subtotal">0 دينار عراقي</span>
                    </div>
                    <div class="order-summary__item">
                        <span>رسوم التوصيل:</span>
                        <span id="delivery-fee">0 دينار عراقي</span>
                    </div>
                    <div class="order-summary__item order-summary__total">
                        <span>المجموع الكلي:</span>
                        <span id="order-total">0 دينار عراقي</span>
                    </div>
                </div>
                <button type="submit" class="btn btn--primary btn--full">تأكيد الطلب</button>
            </form>
        </div>
    </div>

    <!-- Success Modal -->
    <div class="modal" id="success-modal">
        <div class="modal__content">
            <div class="success-content">
                <div class="success-icon">
                    <i class="ri-check-line"></i>
                </div>
                <h3 class="success-title">تم الطلب بنجاح!</h3>
                <p class="success-message">
                    شكراً لك على طلبك. سنتواصل معك قريباً لتأكيد التفاصيل والتوصيل.
                </p>
                <button class="btn btn--primary" id="back-to-store">العودة للمتجر</button>
            </div>
        </div>
    </div>

    <!-- Overlay -->
    <div class="overlay" id="overlay"></div>

    <!-- JavaScript -->
    <script src="script.js"></script>
</body>
</html>