/*=============== SHOW MENU ===============*/
const navMenu = document.getElementById('nav-menu');
const navToggle = document.getElementById('nav-toggle');
const navClose = document.getElementById('nav-close');

if (navToggle) {
    navToggle.addEventListener('click', () => {
        navMenu.classList.add('show');
    });
}

if (navClose) {
    navClose.addEventListener('click', () => {
        navMenu.classList.remove('show');
    });
}

/*=============== REMOVE MENU MOBILE ===============*/
const navLinks = document.querySelectorAll('.nav__link');

const linkAction = () => {
    const navMenu = document.getElementById('nav-menu');
    navMenu.classList.remove('show');
}
navLinks.forEach(n => n.addEventListener('click', linkAction));

/*=============== CHANGE BACKGROUND HEADER ===============*/
const scrollHeader = () => {
    const header = document.querySelector('.header');
    if (header) {
        window.scrollY >= 50 ? header.classList.add('scroll-header')
                             : header.classList.remove('scroll-header');
    }
}

/*=============== DARK LIGHT THEME ===============*/
const themeButton = document.getElementById('theme-toggle');
const darkTheme = 'dark-theme';
const iconTheme = 'ri-sun-line';

// Previously selected topic (if user selected)
const selectedTheme = localStorage.getItem('selected-theme');
const selectedIcon = localStorage.getItem('selected-icon');

// We obtain the current theme that the interface has by validating the dark-theme class
const getCurrentTheme = () => document.body.classList.contains(darkTheme) ? 'dark' : 'light';
const getCurrentIcon = () => themeButton.classList.contains(iconTheme) ? 'ri-moon-line' : 'ri-sun-line';

// We validate if the user previously chose a topic
if (selectedTheme) {
    document.body.classList[selectedTheme === 'dark' ? 'add' : 'remove'](darkTheme);
    themeButton.classList[selectedIcon === 'ri-moon-line' ? 'add' : 'remove'](iconTheme);
}

// Activate / deactivate the theme manually with the button
themeButton.addEventListener('click', () => {
    // Add or remove the dark / icon theme
    document.body.classList.toggle(darkTheme);
    themeButton.classList.toggle(iconTheme);
    // We save the theme and the current icon that the user chose
    localStorage.setItem('selected-theme', getCurrentTheme());
    localStorage.setItem('selected-icon', getCurrentIcon());
});

/*=============== PRODUCTS DATA ===============*/
const products = [
    {
        id: 1,
        name: 'جوزية',
        price: 7000,
        image: 'images/placeholder.svg',
        description: 'قطع جوزية محشية ومغطاة بطبقة شوكولاتة فاخرة',
        options: ['الكراميل', 'الشوكولاتة', 'البستاشيو']
    },
    {
        id: 2,
        name: 'مادلين كيك',
        price: 4500,
        image: 'images/placeholder.svg',
        description: 'كيك ناعم ومميز مغطى بشوكولاتة فاخرة ومزين بالبندق المحمص',
        options: ['بالبندق']
    },
    {
        id: 3,
        name: 'حلى ڤيلا',
        price: 6000,
        image: 'images/placeholder.svg',
        description: 'حلى فاخر بطبقات متعددة من الكريمة والفول السوداني المقرمش',
        options: ['بالفول السوداني']
    }
];

/*=============== CART FUNCTIONALITY ===============*/
let cart = JSON.parse(localStorage.getItem('cart')) || [];
let favorites = JSON.parse(localStorage.getItem('favorites')) || [];

// Update cart count
const updateCartCount = () => {
    const cartCount = document.getElementById('cart-count');
    const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
    cartCount.textContent = totalItems;
};

// Update favorites count
const updateFavoritesCount = () => {
    const favoritesCount = document.getElementById('favorites-count');
    favoritesCount.textContent = favorites.length;
};

// Add to cart
const addToCart = (productId) => {
    const product = products.find(p => p.id === productId);
    const selectedOption = document.querySelector(`[data-id="${productId}"]`).value;
    
    const existingItem = cart.find(item => item.id === productId && item.option === selectedOption);
    
    if (existingItem) {
        existingItem.quantity += 1;
    } else {
        cart.push({
            id: productId,
            name: product.name,
            price: product.price,
            image: product.image,
            option: selectedOption,
            quantity: 1
        });
    }
    
    localStorage.setItem('cart', JSON.stringify(cart));
    updateCartCount();
    updateCartDisplay();
    
    // Show success message
    showNotification('تم إضافة المنتج إلى السلة');
};

// Remove from cart
const removeFromCart = (productId, option) => {
    cart = cart.filter(item => !(item.id === productId && item.option === option));
    localStorage.setItem('cart', JSON.stringify(cart));
    updateCartCount();
    updateCartDisplay();
};

// Update cart item quantity
const updateCartQuantity = (productId, option, change) => {
    const item = cart.find(item => item.id === productId && item.option === option);
    if (item) {
        item.quantity += change;
        if (item.quantity <= 0) {
            removeFromCart(productId, option);
        } else {
            localStorage.setItem('cart', JSON.stringify(cart));
            updateCartCount();
            updateCartDisplay();
        }
    }
};

// Display cart items
const updateCartDisplay = () => {
    const cartContent = document.getElementById('cart-content');
    const cartTotal = document.getElementById('cart-total');
    
    if (cart.length === 0) {
        cartContent.innerHTML = `
            <div class="cart-empty">
                <i class="ri-shopping-cart-line"></i>
                <p>السلة فارغة</p>
            </div>
        `;
        cartTotal.textContent = '0';
        return;
    }
    
    let total = 0;
    cartContent.innerHTML = cart.map(item => {
        total += item.price * item.quantity;
        return `
            <div class="cart-item">
                <img src="${item.image}" alt="${item.name}" class="cart-item__image">
                <div class="cart-item__content">
                    <h4 class="cart-item__title">${item.name}</h4>
                    <p class="cart-item__option">${item.option}</p>
                    <p class="cart-item__price">${item.price.toLocaleString()} دينار عراقي</p>
                </div>
                <div class="cart-item__controls">
                    <button class="cart-item__btn" onclick="updateCartQuantity(${item.id}, '${item.option}', -1)">
                        <i class="ri-subtract-line"></i>
                    </button>
                    <span class="cart-item__quantity">${item.quantity}</span>
                    <button class="cart-item__btn" onclick="updateCartQuantity(${item.id}, '${item.option}', 1)">
                        <i class="ri-add-line"></i>
                    </button>
                </div>
            </div>
        `;
    }).join('');
    
    cartTotal.textContent = total.toLocaleString();
};

/*=============== FAVORITES FUNCTIONALITY ===============*/
// Toggle favorite
const toggleFavorite = (productId) => {
    const product = products.find(p => p.id === productId);
    const favoriteButton = document.querySelector(`[data-id="${productId}"].product__favorite`);
    
    const existingIndex = favorites.findIndex(item => item.id === productId);
    
    if (existingIndex > -1) {
        favorites.splice(existingIndex, 1);
        favoriteButton.classList.remove('active');
        showNotification('تم إزالة المنتج من المفضلة');
    } else {
        favorites.push({
            id: productId,
            name: product.name,
            price: product.price,
            image: product.image,
            option: product.options[0]
        });
        favoriteButton.classList.add('active');
        showNotification('تم إضافة المنتج إلى المفضلة');
    }
    
    localStorage.setItem('favorites', JSON.stringify(favorites));
    updateFavoritesCount();
    updateFavoritesDisplay();
};

// Remove from favorites
const removeFromFavorites = (productId) => {
    favorites = favorites.filter(item => item.id !== productId);
    localStorage.setItem('favorites', JSON.stringify(favorites));
    updateFavoritesCount();
    updateFavoritesDisplay();
    
    // Update button state
    const favoriteButton = document.querySelector(`[data-id="${productId}"].product__favorite`);
    if (favoriteButton) {
        favoriteButton.classList.remove('active');
    }
};

// Clear all favorites
const clearFavorites = () => {
    favorites = [];
    localStorage.setItem('favorites', JSON.stringify(favorites));
    updateFavoritesCount();
    updateFavoritesDisplay();
    
    // Update all favorite buttons
    document.querySelectorAll('.product__favorite').forEach(btn => {
        btn.classList.remove('active');
    });
    
    showNotification('تم مسح جميع المفضلة');
};

// Display favorites
const updateFavoritesDisplay = () => {
    const favoritesContent = document.getElementById('favorites-content');
    
    if (favorites.length === 0) {
        favoritesContent.innerHTML = `
            <div class="favorites-empty">
                <i class="ri-heart-line"></i>
                <p>لا توجد منتجات مفضلة</p>
            </div>
        `;
        return;
    }
    
    favoritesContent.innerHTML = favorites.map(item => `
        <div class="favorite-item">
            <img src="${item.image}" alt="${item.name}" class="favorite-item__image">
            <div class="favorite-item__content">
                <h4 class="favorite-item__title">${item.name}</h4>
                <p class="favorite-item__option">${item.option}</p>
                <p class="favorite-item__price">${item.price.toLocaleString()} دينار عراقي</p>
            </div>
            <button class="favorite-item__remove" onclick="removeFromFavorites(${item.id})">
                <i class="ri-close-line"></i>
            </button>
        </div>
    `).join('');
};

/*=============== SIDEBAR FUNCTIONALITY ===============*/
const cartToggle = document.getElementById('cart-toggle');
const cartSidebar = document.getElementById('cart-sidebar');
const cartClose = document.getElementById('cart-close');

const favoritesToggle = document.getElementById('favorites-toggle');
const favoritesSidebar = document.getElementById('favorites-sidebar');
const favoritesClose = document.getElementById('favorites-close');

const overlay = document.getElementById('overlay');

// Show cart sidebar
cartToggle.addEventListener('click', () => {
    cartSidebar.classList.add('show');
    overlay.classList.add('show');
});

// Hide cart sidebar
cartClose.addEventListener('click', () => {
    cartSidebar.classList.remove('show');
    overlay.classList.remove('show');
});

// Show favorites sidebar
favoritesToggle.addEventListener('click', () => {
    favoritesSidebar.classList.add('show');
    overlay.classList.add('show');
});

// Hide favorites sidebar
favoritesClose.addEventListener('click', () => {
    favoritesSidebar.classList.remove('show');
    overlay.classList.remove('show');
});

// Hide sidebars when clicking overlay
overlay.addEventListener('click', () => {
    cartSidebar.classList.remove('show');
    favoritesSidebar.classList.remove('show');
    overlay.classList.remove('show');
});

/*=============== NOTIFICATION SYSTEM ===============*/
const showNotification = (message) => {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.textContent = message;
    
    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background-color: var(--first-color);
        color: var(--white-color);
        padding: 1rem 1.5rem;
        border-radius: 0.5rem;
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        font-size: var(--small-font-size);
        font-weight: var(--font-medium);
        box-shadow: 0 4px 16px var(--shadow-color);
    `;
    
    document.body.appendChild(notification);
    
    // Show notification
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Hide notification
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
};

/*=============== ORDER FUNCTIONALITY ===============*/
const orderNowBtn = document.getElementById('order-now');
const orderModal = document.getElementById('order-modal');
const orderModalClose = document.getElementById('order-modal-close');
const orderForm = document.getElementById('order-form');
const successModal = document.getElementById('success-modal');
const backToStoreBtn = document.getElementById('back-to-store');

// Show order modal
orderNowBtn.addEventListener('click', () => {
    if (cart.length === 0) {
        showNotification('السلة فارغة! أضف منتجات أولاً');
        return;
    }

    orderModal.classList.add('show');
    updateOrderSummary();
});

// Hide order modal
orderModalClose.addEventListener('click', () => {
    orderModal.classList.remove('show');
});

// Update order summary
const updateOrderSummary = () => {
    const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const provinceSelect = document.querySelector('[name="province"]');

    document.getElementById('order-subtotal').textContent = `${subtotal.toLocaleString()} دينار عراقي`;

    // Update delivery fee based on province
    provinceSelect.addEventListener('change', () => {
        const province = provinceSelect.value;
        let deliveryFee = 0;

        if (province === 'البصرة') {
            deliveryFee = 3000;
        } else if (province && province !== '') {
            deliveryFee = 5000;
        }

        document.getElementById('delivery-fee').textContent = `${deliveryFee.toLocaleString()} دينار عراقي`;
        document.getElementById('order-total').textContent = `${(subtotal + deliveryFee).toLocaleString()} دينار عراقي`;
    });

    // Initial calculation
    document.getElementById('delivery-fee').textContent = '0 دينار عراقي';
    document.getElementById('order-total').textContent = `${subtotal.toLocaleString()} دينار عراقي`;
};

// Handle order form submission
orderForm.addEventListener('submit', (e) => {
    e.preventDefault();

    const formData = new FormData(orderForm);
    const orderData = {
        customer: {
            fullName: formData.get('fullName'),
            phone: formData.get('phone'),
            province: formData.get('province'),
            address: formData.get('address')
        },
        items: cart,
        subtotal: cart.reduce((sum, item) => sum + (item.price * item.quantity), 0),
        deliveryFee: formData.get('province') === 'البصرة' ? 3000 : 5000,
        total: cart.reduce((sum, item) => sum + (item.price * item.quantity), 0) +
               (formData.get('province') === 'البصرة' ? 3000 : 5000),
        orderDate: new Date().toISOString(),
        status: 'pending'
    };

    // Save order to localStorage
    const orders = JSON.parse(localStorage.getItem('orders')) || [];
    orders.push(orderData);
    localStorage.setItem('orders', JSON.stringify(orders));

    // Clear cart
    cart = [];
    localStorage.setItem('cart', JSON.stringify(cart));
    updateCartCount();
    updateCartDisplay();

    // Hide order modal and show success modal
    orderModal.classList.remove('show');
    successModal.classList.add('show');

    // Hide cart sidebar
    cartSidebar.classList.remove('show');
    overlay.classList.remove('show');
});

// Back to store button
backToStoreBtn.addEventListener('click', () => {
    successModal.classList.remove('show');
    orderForm.reset();
});

/*=============== CONTACT FORM ===============*/
const contactForm = document.getElementById('contact-form');

contactForm.addEventListener('submit', (e) => {
    e.preventDefault();

    // Get form data
    const formData = new FormData(contactForm);
    const contactData = {
        name: formData.get('name') || contactForm.querySelector('input[type="text"]').value,
        email: formData.get('email') || contactForm.querySelector('input[type="email"]').value,
        message: formData.get('message') || contactForm.querySelector('textarea').value,
        date: new Date().toISOString()
    };

    // Save to localStorage
    const messages = JSON.parse(localStorage.getItem('contact-messages')) || [];
    messages.push(contactData);
    localStorage.setItem('contact-messages', JSON.stringify(messages));

    // Show success message
    showNotification('تم إرسال رسالتك بنجاح! سنتواصل معك قريباً');

    // Reset form
    contactForm.reset();
});

/*=============== SMOOTH SCROLLING ===============*/
const scrollLinks = document.querySelectorAll('a[href^="#"]');

scrollLinks.forEach(link => {
    link.addEventListener('click', (e) => {
        e.preventDefault();

        const targetId = link.getAttribute('href');
        const targetSection = document.querySelector(targetId);

        if (targetSection) {
            const headerHeight = document.querySelector('.header').offsetHeight;
            const targetPosition = targetSection.offsetTop - headerHeight;

            window.scrollTo({
                top: targetPosition,
                behavior: 'smooth'
            });
        }
    });
});

/*=============== SCROLL UP ===============*/
const scrollUp = () => {
    const scrollUpBtn = document.createElement('a');
    scrollUpBtn.href = '#home';
    scrollUpBtn.className = 'scrollup';
    scrollUpBtn.innerHTML = '<i class="ri-arrow-up-line"></i>';

    document.body.appendChild(scrollUpBtn);

    const showScrollUp = () => {
        if (window.scrollY >= 350) {
            scrollUpBtn.classList.add('show-scroll');
        } else {
            scrollUpBtn.classList.remove('show-scroll');
        }
    };

    window.addEventListener('scroll', showScrollUp);

    scrollUpBtn.addEventListener('click', (e) => {
        e.preventDefault();
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
};

/*=============== LOADING SCREEN ===============*/
window.addEventListener('load', () => {
    const loading = document.getElementById('loading');
    setTimeout(() => {
        loading.classList.add('hide');
        setTimeout(() => {
            loading.style.display = 'none';
        }, 500);
    }, 1000);
});

/*=============== INTERSECTION OBSERVER FOR ANIMATIONS ===============*/
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.classList.add('fade-in');
            observer.unobserve(entry.target);
        }
    });
}, observerOptions);

// Observe elements for animation
const animateElements = document.querySelectorAll('.product__card, .about__card, .faq__item');
animateElements.forEach(el => observer.observe(el));

/*=============== PERFORMANCE OPTIMIZATIONS ===============*/
// Debounce function for scroll events
const debounce = (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
};

// Optimized scroll handler
const optimizedScrollHandler = debounce(() => {
    scrollHeader();
    scrollActive();
}, 10);

window.addEventListener('scroll', optimizedScrollHandler);

/*=============== INITIALIZE APP ===============*/
document.addEventListener('DOMContentLoaded', () => {
    // Initialize counts
    updateCartCount();
    updateFavoritesCount();

    // Initialize displays
    updateCartDisplay();
    updateFavoritesDisplay();

    // Set up favorite buttons
    favorites.forEach(item => {
        const favoriteButton = document.querySelector(`[data-id="${item.id}"].product__favorite`);
        if (favoriteButton) {
            favoriteButton.classList.add('active');
        }
    });

    // Set up product event listeners
    document.querySelectorAll('.add-to-cart').forEach(btn => {
        btn.addEventListener('click', () => {
            const productId = parseInt(btn.getAttribute('data-id'));
            addToCart(productId);
        });
    });

    document.querySelectorAll('.product__favorite').forEach(btn => {
        btn.addEventListener('click', () => {
            const productId = parseInt(btn.getAttribute('data-id'));
            toggleFavorite(productId);
        });
    });

    // Set up clear favorites button
    document.getElementById('clear-favorites').addEventListener('click', clearFavorites);

    // Initialize scroll up
    scrollUp();

    // Close modals when clicking outside
    window.addEventListener('click', (e) => {
        if (e.target === orderModal) {
            orderModal.classList.remove('show');
        }
        if (e.target === successModal) {
            successModal.classList.remove('show');
        }
    });
});

/*=============== ACTIVE LINK ===============*/
const sections = document.querySelectorAll('section[id]');

const scrollActive = () => {
    const scrollY = window.pageYOffset;

    sections.forEach(current => {
        const sectionHeight = current.offsetHeight;
        const sectionTop = current.offsetTop - 58;
        const sectionId = current.getAttribute('id');
        const sectionsClass = document.querySelector('.nav__menu a[href*=' + sectionId + ']');

        if (scrollY > sectionTop && scrollY <= sectionTop + sectionHeight) {
            sectionsClass?.classList.add('active-link');
        } else {
            sectionsClass?.classList.remove('active-link');
        }
    });
};

// Scroll active is now handled by optimizedScrollHandler

/*=============== FAQ FUNCTIONALITY ===============*/
const faqItems = document.querySelectorAll('.faq__item');

faqItems.forEach(item => {
    const question = item.querySelector('.faq__question');

    question.addEventListener('click', () => {
        const isActive = item.classList.contains('active');

        // Close all FAQ items
        faqItems.forEach(faqItem => {
            faqItem.classList.remove('active');
        });

        // Open clicked item if it wasn't active
        if (!isActive) {
            item.classList.add('active');
        }
    });
});
